"""
FastAPI Service for Mastercard Priceless Experiences RAG
Session-based conversation management with enhanced JSON responses
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
import uuid
import json
from datetime import datetime

from langchain_rag_workflow import MastercardRAGChain, EnhancedResponse


# Pydantic Models
class QueryRequest(BaseModel):
    query: str = Field(..., description="User query about Mastercard experiences")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")


class QueryResponse(BaseModel):
    response: str = Field(..., description="Main response text for display")
    image_url: Optional[str] = Field(None, description="Image URL for display")
    context: Optional[Dict[str, Any]] = Field(None, description="Context data for follow-up queries")
    session_id: str = Field(..., description="Session ID for this conversation")
    timestamp: str = Field(..., description="Response timestamp")


class SessionHistoryResponse(BaseModel):
    session_id: str
    history: List[Dict[str, Any]]
    total_messages: int


class HealthResponse(BaseModel):
    status: str
    timestamp: str
    version: str


# FastAPI App
app = FastAPI(
    title="Mastercard Priceless Experiences RAG API",
    description="AI-powered assistant for Mastercard Priceless Experiences with conversation memory",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global RAG chain instance
rag_chain: Optional[MastercardRAGChain] = None


async def get_rag_chain() -> MastercardRAGChain:
    """Dependency to get RAG chain instance"""
    global rag_chain
    if rag_chain is None:
        raise HTTPException(status_code=500, detail="RAG chain not initialized")
    return rag_chain


@app.on_event("startup")
async def startup_event():
    """Initialize RAG chain on startup"""
    global rag_chain
    try:
        rag_chain = MastercardRAGChain(
            experiences_data_path="rag_chunked_product_no_vectors.json",
            model_name="llama-maverick"
        )
        print("✅ RAG chain initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize RAG chain: {e}")
        raise


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        version="1.0.0"
    )


@app.post("/query", response_model=QueryResponse)
async def process_query(
    request: QueryRequest,
    rag_chain: MastercardRAGChain = Depends(get_rag_chain)
):
    """
    Process user query with session-based conversation memory
    
    Returns enhanced JSON response with context preservation for follow-up queries
    """
    try:
        # Generate session ID if not provided
        session_id = request.session_id or str(uuid.uuid4())
        
        # Process query
        enhanced_response = rag_chain.process_query(
            query=request.query,
            session_id=session_id
        )
        
        # Convert to API response format
        return QueryResponse(
            response=enhanced_response.response,
            image_url=enhanced_response.image_url,
            context=enhanced_response.to_dict()["context"],
            session_id=session_id,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing query: {str(e)}"
        )


@app.get("/session/{session_id}/history", response_model=SessionHistoryResponse)
async def get_session_history(
    session_id: str,
    rag_chain: MastercardRAGChain = Depends(get_rag_chain)
):
    """Get conversation history for a specific session"""
    try:
        history = rag_chain.get_session_history(session_id)
        
        return SessionHistoryResponse(
            session_id=session_id,
            history=history,
            total_messages=len(history)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving session history: {str(e)}"
        )


@app.delete("/session/{session_id}")
async def clear_session(
    session_id: str,
    rag_chain: MastercardRAGChain = Depends(get_rag_chain)
):
    """Clear conversation history for a specific session"""
    try:
        rag_chain.clear_session(session_id)
        return {"message": f"Session {session_id} cleared successfully"}
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error clearing session: {str(e)}"
        )


@app.get("/sessions")
async def list_active_sessions(
    rag_chain: MastercardRAGChain = Depends(get_rag_chain)
):
    """List all active session IDs"""
    try:
        active_sessions = list(rag_chain.memory_manager.sessions.keys())
        return {
            "active_sessions": active_sessions,
            "total_sessions": len(active_sessions)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing sessions: {str(e)}"
        )


# Example usage endpoints for testing
@app.get("/examples/conversation")
async def example_conversation():
    """Example conversation flow for testing"""
    return {
        "example_flow": [
            {
                "step": 1,
                "user_query": "Hello",
                "expected_response_format": {
                    "response": "Hello! I'm your Mastercard Priceless Experiences assistant...",
                    "image_url": None,
                    "context": None
                }
            },
            {
                "step": 2,
                "user_query": "Tell me about underwater restaurants in Thailand",
                "expected_response_format": {
                    "response": "Su Va Na offers an extraordinary underwater dining experience...",
                    "image_url": "https://d5xydlzdo08s0.cloudfront.net/...",
                    "context": {
                        "experience_id": "209677",
                        "title": "Soak in privileged treatment at Asia's largest underwater restaurant",
                        "pricing": {"amount": 5900, "currency": "THB", "for_people": 1},
                        "location": {"city": "Ko Kaeo", "country": "Thailand"},
                        "terms_and_conditions": "Full terms...",
                        "booking_requirements": "Must be booked 7 days in advance..."
                    }
                }
            },
            {
                "step": 3,
                "user_query": "How can I book this?",
                "expected_response_format": {
                    "response": "To book Su Va Na, make a reservation at least 7 days in advance...",
                    "image_url": None,
                    "context": {"booking_requirements": "..."}
                }
            }
        ]
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "fastapi_rag_service:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
