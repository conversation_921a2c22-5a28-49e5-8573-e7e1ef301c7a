import json
import re
import datetime
from typing import List, Dict, Any, Optional
import html

def extract_highlights(long_description: str) -> List[str]:
    """Extract highlight bullet points from the HTML description."""
    highlights = []
    # Look for bullet points in the HTML
    bullet_pattern = r'<li>(.*?)</li>'
    matches = re.findall(bullet_pattern, long_description)
    
    # Only take bullet points that appear after "The highlights" section
    if "The highlights" in long_description:
        for match in matches:
            # Clean up any HTML tags
            clean_text = re.sub(r'<.*?>', '', match).strip()
            if clean_text:
                highlights.append(clean_text)
    
    return highlights

def extract_priceless_value(long_description: str) -> Optional[str]:
    """Extract the 'What's priceless' value from the description."""
    if "What's priceless" in long_description:
        # Find text after "What's priceless" and before the next HTML tag
        pattern = r"What's\s+<em>priceless</em></strong></p>\s*<p>(.*?)</p>"
        match = re.search(pattern, long_description)
        if match:
            return match.group(1).strip()
    return None

def extract_short_description(description: str) -> str:
    """Extract the short description."""
    if description:
        # Clean up any HTML tags
        return re.sub(r'<.*?>', '', description).strip()
    return ""

def extract_terms_conditions(details: str) -> List[str]:
    """Extract terms and conditions from the details HTML."""
    terms = []
    # Look for bullet points in the HTML
    bullet_pattern = r'<li>(.*?)</li>'
    matches = re.findall(bullet_pattern, details)
    
    for match in matches:
        # Clean up any HTML tags
        clean_text = re.sub(r'<.*?>', '', match).strip()
        if clean_text:
            terms.append(clean_text)
    
    return terms[:5]  # Limit to first 5 terms for brevity

def extract_keywords(product: Dict[str, Any]) -> List[str]:
    """Generate keywords based on product data."""
    keywords = []
    
    # Add category names
    if "categories" in product and isinstance(product["categories"], list):
        for category in product["categories"]:
            if isinstance(category, dict) and "name" in category:
                if category["name"] not in ["pchomepage", "Global", "Global Checkout Catalog", "Global Content Catalog"]:
                    keywords.append(category["name"].lower())
    
    # Add location keywords
    if "eventCity" in product and product["eventCity"]:
        city_parts = product["eventCity"].split(",")
        keywords.append(city_parts[0].strip().lower())
        
        if len(city_parts) > 1:
            country = city_parts[1].strip()
            if country:
                keywords.append(country.lower())
    
    # Add venue if available
    if "eventVenue" in product and product["eventVenue"]:
        keywords.append(product["eventVenue"].lower())
    
    # Extract key terms from display name
    if "displayName" in product:
        # Split by common separators and add meaningful words
        name_parts = re.split(r'[.,;:\-()]', product["displayName"])
        for part in name_parts:
            words = part.strip().split()
            if len(words) <= 3 and len(words) > 0 and len(part.strip()) > 5:
                keywords.append(part.strip().lower())
    
    # Add menu type if in name
    menu_types = ["vegan", "vegetarian", "pescatarian", "seafood", "non seafood", "spring"]
    if "name" in product:
        for menu_type in menu_types:
            if menu_type.lower() in product["name"].lower():
                keywords.append(menu_type.lower() + " menu")
    
    # Remove duplicates and limit to 10 keywords
    unique_keywords = []
    for keyword in keywords:
        if keyword not in unique_keywords:
            unique_keywords.append(keyword)
    
    return unique_keywords[:10]

def extract_end_date(end_date: str) -> Optional[str]:
    """Extract and format end date."""
    if end_date:
        try:
            # Parse the date format
            date_pattern = r'(\d{4}-\d{2}-\d{2})'
            match = re.search(date_pattern, end_date)
            if match:
                return match.group(1)
        except:
            pass
    return None

def extract_vendor_description(vendor_html: str) -> str:
    """Extract vendor description from HTML."""
    if vendor_html:
        desc_pattern = r'<div class="desc body-text"><p>(.*?)</p>'
        match = re.search(desc_pattern, vendor_html)
        if match:
            return match.group(1).strip()
    return ""

def extract_people_count(display_name: str) -> int:
    """Extract number of people from display name."""
    match = re.search(r'For (\d+) (person|people)', display_name)
    if match:
        return int(match.group(1))
    return 1  # Default to 1 if not specified

def convert_to_rag_format(input_file: str, output_file: str):
    """Convert product data to optimized RAG format."""
    try:
        # Load the input JSON data
        with open(input_file, 'r', encoding='utf-8') as f:
            products_data = json.load(f)
        
        # Create the new structure
        rag_data = {
            "schema_version": "1.0",
            "products": [],
            "metadata": {
                "total_count": len(products_data),
                "generated_at": datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ"),
                "source": "Mastercard Priceless Experiences",
                "embedding_model": "text-embedding-ada-002"
            }
        }
        
        # Process each product
        for product_item in products_data:
            if product_item.get("msg") != "OK" or "data" not in product_item:
                continue
                
            product = product_item["data"]
            
            # Extract short description and highlights
            short_description = extract_short_description(product.get("description", ""))
            highlights = extract_highlights(product.get("longDescription", ""))
            priceless_value = extract_priceless_value(product.get("longDescription", ""))
            
            # Extract terms and conditions
            terms = extract_terms_conditions(product.get("details", ""))
            
            # Extract people count
            people_count = extract_people_count(product.get("displayName", ""))
            
            # Extract end date
            end_date = extract_end_date(product.get("endDate", ""))
            
            # Extract categories
            categories = []
            if "categories" in product and isinstance(product["categories"], list):
                for category in product["categories"]:
                    if isinstance(category, dict) and "name" in category:
                        categories.append(category["name"])
            
            # Create the optimized product entry
            rag_product = {
                "id": str(product.get("id", "")),
                "type": "experience" if product.get("isExperience", False) else "product",
                "title": product.get("name", ""),
                "description": {
                    "short": short_description,
                    "highlights": highlights,
                }
            }
            
            # Add priceless value if available
            if priceless_value:
                rag_product["description"]["priceless_value"] = priceless_value
            
            # Add pricing information
            rag_product["pricing"] = {
                "amount": product.get("price", 0),
                "currency": product.get("currencyCode", ""),
                "for_people": people_count
            }
            
            # Add location information
            location = {
                "city": product.get("eventCity", "").split(",")[0].strip(),
                "venue": product.get("eventVenue", "")
            }
            
            # Add region/state if available
            if product.get("eventState"):
                location["region"] = product.get("eventState")
            
            # Add country if in city string
            city_parts = product.get("eventCity", "").split(",")
            if len(city_parts) > 1:
                location["country"] = city_parts[1].strip()
            
            # Add coordinates if available
            if product.get("latitude") and product.get("longitude"):
                location["coordinates"] = {
                    "latitude": product.get("latitude"),
                    "longitude": product.get("longitude")
                }
            
            # Add address if available
            if "eventAddress" in product and product["eventAddress"]:
                location["address"] = {
                    "line1": product["eventAddress"].get("addressLine1", ""),
                    "line2": product["eventAddress"].get("addressLine2", ""),
                    "city": product["eventAddress"].get("city", ""),
                    "state": product["eventAddress"].get("stateName", ""),
                    "country_code": product["eventAddress"].get("countryCode", ""),
                    "postal_code": product["eventAddress"].get("postalCode", "")
                }
            
            rag_product["location"] = location
            
            # Add media information
            rag_product["media"] = {
                "url": product.get("url", ""),
                "image_url": product.get("imageURL", "")
            }
            
            # Add end date if available
            if end_date:
                rag_product["valid_until"] = end_date
            
            # Add provider information if available
            if "vendor" in product and product["vendor"]:
                vendor_description = extract_vendor_description(product["vendor"].get("html", ""))
                
                rag_product["provider"] = {
                    "id": str(product["vendor"].get("id", "")),
                    "name": product["vendor"].get("name", "")
                }
                
                if vendor_description:
                    rag_product["provider"]["description"] = vendor_description
            
            # Add categories
            rag_product["categories"] = categories
            
            # Add primary category if available
            if "primaryCategory" in product and product["primaryCategory"]:
                rag_product["primary_category"] = product["primaryCategory"].get("name", "")
            
            # Add terms and conditions
            if terms:
                rag_product["terms_and_conditions"] = terms
            
            # Generate keywords
            rag_product["keywords"] = extract_keywords(product)
            
            # Add placeholder for vector embeddings
            rag_product["vector_embeddings"] = {
                "description": "[PLACEHOLDER FOR VECTOR EMBEDDING OF DESCRIPTION]",
                "categories": "[PLACEHOLDER FOR VECTOR EMBEDDING OF CATEGORIES]",
                "location": "[PLACEHOLDER FOR VECTOR EMBEDDING OF LOCATION]"
            }
            
            # Add to products list
            rag_data["products"].append(rag_product)
        
        # Save the optimized RAG format
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(rag_data, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully converted {len(rag_data['products'])} products to RAG format in {output_file}")
        
    except Exception as e:
        print(f"Error converting to RAG format: {str(e)}")

if __name__ == "__main__":
    input_file = "product-product_id.json"
    output_file = "rag_optimized_product.json"
    convert_to_rag_format(input_file, output_file)
