import json
import uuid
import re
import html
from typing import Dict, List, Any

def clean_html_text(text: str) -> str:
    """
    Clean HTML text using NLP text cleaning techniques.

    Args:
        text: Raw HTML text

    Returns:
        Cleaned text
    """
    if not text or not isinstance(text, str):
        return ""

    # Decode HTML entities
    text = html.unescape(text)

    # Remove HTML tags but preserve content
    text = re.sub(r'<[^>]+>', ' ', text)

    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text).strip()

    # Remove special characters but keep punctuation
    text = re.sub(r'[^\w\s.,!?;:\-()&]', '', text)

    return text

def extract_highlights_from_html(html_text: str) -> List[str]:
    """
    Extract bullet points from HTML text.

    Args:
        html_text: HTML text containing bullet points

    Returns:
        List of cleaned bullet points
    """
    if not html_text:
        return []

    # Extract content between <li> tags
    li_pattern = r'<li>(.*?)</li>'
    matches = re.findall(li_pattern, html_text, re.DOTALL)

    highlights = []
    for match in matches:
        cleaned = clean_html_text(match)
        if cleaned and len(cleaned) > 10:  # Filter out very short items
            highlights.append(cleaned)

    return highlights

def extract_priceless_value(html_text: str) -> str:
    """
    Extract the 'What's priceless' value from HTML text.

    Args:
        html_text: HTML text containing priceless value

    Returns:
        Cleaned priceless value text
    """
    if not html_text or "priceless" not in html_text.lower():
        return ""

    # Look for text after "What's priceless"
    pattern = r"What's\s+<em>priceless</em>.*?<p>(.*?)</p>"
    match = re.search(pattern, html_text, re.DOTALL | re.IGNORECASE)

    if match:
        return clean_html_text(match.group(1))

    return ""



def process_product_data(product_item: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a single product item with NLP text cleaning.

    Args:
        product_item: Raw product data

    Returns:
        Cleaned and structured product data
    """
    if product_item.get("msg") != "OK" or "data" not in product_item:
        return None

    product = product_item["data"]

    # Extract and clean basic information
    cleaned_product = {
        "id": str(product.get("id", "")),
        "name": clean_html_text(product.get("name", "")),
        "display_name": clean_html_text(product.get("displayName", "")),
        "price": product.get("price", 0),
        "currency_code": product.get("currencyCode", ""),
        "currency_character": product.get("currencyCharacter", ""),
        "type": product.get("type", {}).get("name", ""),
        "is_experience": product.get("isExperience", False),
        "is_event": product.get("isEvent", False)
    }

    # Clean descriptions
    long_desc = product.get("longDescription", "")
    cleaned_product["description_short"] = clean_html_text(product.get("description", ""))
    cleaned_product["description_long"] = clean_html_text(long_desc)

    # Extract highlights and priceless value
    cleaned_product["highlights"] = extract_highlights_from_html(long_desc)
    cleaned_product["priceless_value"] = extract_priceless_value(long_desc)

    # Clean details and terms
    details_html = product.get("details", "")
    cleaned_product["terms_and_conditions"] = extract_highlights_from_html(details_html)

    # Location information
    cleaned_product["event_city"] = product.get("eventCity", "")
    cleaned_product["event_state"] = product.get("eventState", "")
    cleaned_product["event_venue"] = product.get("eventVenue", "")
    cleaned_product["latitude"] = product.get("latitude")
    cleaned_product["longitude"] = product.get("longitude")

    # Address information
    if "eventAddress" in product and product["eventAddress"]:
        address = product["eventAddress"]
        cleaned_product["address"] = {
            "line1": address.get("addressLine1", ""),
            "line2": address.get("addressLine2", ""),
            "city": address.get("city", ""),
            "state": address.get("stateName", ""),
            "country_code": address.get("countryCode", ""),
            "postal_code": address.get("postalCode", "")
        }

    # Vendor/Provider information
    if "vendor" in product and product["vendor"]:
        vendor = product["vendor"]
        vendor_desc = ""
        if "html" in vendor:
            # Extract description from vendor HTML
            desc_match = re.search(r'<div class="desc body-text"><p>(.*?)</p>', vendor["html"])
            if desc_match:
                vendor_desc = clean_html_text(desc_match.group(1))

        cleaned_product["provider"] = {
            "id": str(vendor.get("id", "")),
            "name": vendor.get("name", ""),
            "description": vendor_desc,
            "url": vendor.get("url", ""),
            "image_url": vendor.get("imageURL", "")
        }

    # Categories
    categories = []
    if "categories" in product and isinstance(product["categories"], list):
        for cat in product["categories"]:
            if isinstance(cat, dict) and "name" in cat:
                categories.append(cat["name"])

    cleaned_product["categories"] = categories

    # Primary category
    if "primaryCategory" in product and product["primaryCategory"]:
        cleaned_product["primary_category"] = product["primaryCategory"].get("name", "")

    # Dates and timing
    cleaned_product["end_date"] = product.get("endDate", "")
    cleaned_product["event_time"] = product.get("eventTime", "")

    # Media
    cleaned_product["image_url"] = product.get("imageURL", "")
    cleaned_product["url"] = product.get("url", "")
    cleaned_product["video_url"] = product.get("videoURL", "")

    # Additional details
    cleaned_product["max_quantity"] = product.get("maxQuantityPerOrder", 1)
    cleaned_product["shared_people_per_item"] = product.get("sharedPeoplePerItem", 1)
    cleaned_product["program_name"] = product.get("programName", "")
    cleaned_product["locale"] = product.get("locale", "")

    return cleaned_product

def flatten_product(product: Dict[str, Any]) -> Dict[str, Any]:
    """
    Flatten a cleaned product dictionary for RAG processing.

    Args:
        product: Cleaned product data

    Returns:
        Flattened product data
    """
    flattened = {}

    for key, value in product.items():
        if isinstance(value, dict):
            # Flatten nested dictionaries
            for sub_key, sub_value in value.items():
                flattened[f"{key}_{sub_key}"] = sub_value
        elif isinstance(value, list):
            # Join simple lists into comma-separated strings
            flattened[key] = ", ".join(str(item) for item in value if item)
        else:
            flattened[key] = value

    return flattened

def create_text_representation(product: Dict[str, Any]) -> str:
    """
    Create a comprehensive text representation for RAG processing.

    Args:
        product: Product data

    Returns:
        Text representation suitable for embeddings
    """
    text_parts = []

    # Basic information
    if product.get("name"):
        text_parts.append(f"Product: {product['name']}")

    if product.get("description_short"):
        text_parts.append(f"Description: {product['description_short']}")

    if product.get("description_long"):
        text_parts.append(f"Detailed Description: {product['description_long']}")

    # Highlights
    if product.get("highlights"):
        highlights_text = "; ".join(product["highlights"])
        text_parts.append(f"Highlights: {highlights_text}")

    # Priceless value
    if product.get("priceless_value"):
        text_parts.append(f"What's Priceless: {product['priceless_value']}")

    # Location information
    location_parts = []
    if product.get("event_venue"):
        location_parts.append(product["event_venue"])
    if product.get("event_city"):
        location_parts.append(product["event_city"])
    if product.get("event_state"):
        location_parts.append(product["event_state"])

    if location_parts:
        text_parts.append(f"Location: {', '.join(location_parts)}")

    # Provider information
    if product.get("provider", {}).get("name"):
        text_parts.append(f"Provider: {product['provider']['name']}")

    if product.get("provider", {}).get("description"):
        text_parts.append(f"Provider Description: {product['provider']['description']}")

    # Categories
    if product.get("categories"):
        categories_text = ", ".join(product["categories"])
        text_parts.append(f"Categories: {categories_text}")

    if product.get("primary_category"):
        text_parts.append(f"Primary Category: {product['primary_category']}")

    # Pricing
    if product.get("price") and product.get("currency_code"):
        text_parts.append(f"Price: {product['price']} {product['currency_code']}")

    # Terms and conditions
    if product.get("terms_and_conditions"):
        terms_text = "; ".join(product["terms_and_conditions"])
        text_parts.append(f"Terms and Conditions: {terms_text}")

    return "\n".join(text_parts)

def generate_keywords(product: Dict[str, Any]) -> List[str]:
    """
    Generate keywords for improved search and retrieval.

    Args:
        product: Product data

    Returns:
        List of keywords
    """
    keywords = set()

    # Add category-based keywords
    if product.get("primary_category"):
        keywords.add(product["primary_category"].lower())

    if product.get("categories"):
        for cat in product["categories"]:
            keywords.add(cat.lower())

    # Add location-based keywords
    if product.get("event_city"):
        keywords.add(product["event_city"].lower())

    if product.get("event_state"):
        keywords.add(product["event_state"].lower())

    # Add provider-based keywords
    if product.get("provider", {}).get("name"):
        keywords.add(product["provider"]["name"].lower())

    # Add type-based keywords
    if product.get("type"):
        keywords.add(product["type"].lower())

    # Add experience/event keywords
    if product.get("is_experience"):
        keywords.add("experience")

    if product.get("is_event"):
        keywords.add("event")

    # Add people count keywords
    if product.get("shared_people_per_item"):
        keywords.add(f"for {product['shared_people_per_item']} person" + ("s" if product['shared_people_per_item'] > 1 else ""))

    return list(keywords)

def process_product_file(input_file: str, output_file: str):
    """
    Process the product-product_id.json file with NLP cleaning and chunking.

    Args:
        input_file: Path to the input JSON file
        output_file: Path to the output JSON file
    """
    print(f"Processing {input_file}...")

    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        raw_data = json.load(f)

    if not isinstance(raw_data, list):
        raise ValueError("Expected input file to contain a list of product items")

    chunks = []
    processed_count = 0

    # Process each product item
    for i, product_item in enumerate(raw_data):
        print(f"Processing product {i+1}/{len(raw_data)}...")

        # Clean and process the product data
        cleaned_product = process_product_data(product_item)

        if cleaned_product is None:
            print(f"Skipping invalid product item {i+1}")
            continue

        # Generate keywords
        keywords = generate_keywords(cleaned_product)
        cleaned_product["keywords"] = keywords

        # Flatten the product for RAG processing
        flattened_product = flatten_product(cleaned_product)

        # Create text representation
        text_representation = create_text_representation(cleaned_product)

        # Create chunk
        chunk = {
            "chunk_id": str(uuid.uuid4()),
            "product_id": cleaned_product["id"],
            "content": flattened_product,
            "text": text_representation,
            "metadata": {
                "name": cleaned_product.get("name", ""),
                "type": cleaned_product.get("type", ""),
                "primary_category": cleaned_product.get("primary_category", ""),
                "end_date": cleaned_product.get("end_date", ""),
                "event_city": cleaned_product.get("event_city", ""),
                "event_state": cleaned_product.get("event_state", ""),
                "provider_name": cleaned_product.get("provider", {}).get("name", ""),
                "price": cleaned_product.get("price", 0),
                "currency_code": cleaned_product.get("currency_code", "")
            }
        }

        chunks.append(chunk)
        processed_count += 1

    # Create output data structure (simplified)
    output_data = {
        "chunks": chunks
    }

    # Save the output JSON data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"Successfully processed {processed_count} products into {len(chunks)} chunks")
    print(f"Output saved to {output_file}")

    # Print summary statistics
    print("\nProcessing Summary:")
    print(f"- Total products processed: {processed_count}")
    print(f"- Total chunks created: {len(chunks)}")
    print(f"- Structure: Simplified (no schema version, chunk_type, or global metadata)")
    print(f"- NLP cleaning applied: Yes")
    print(f"- Keywords generated: Yes")
    print(f"- FAQs excluded: Yes")

def create_embedding_ready_text(chunk: Dict[str, Any]) -> str:
    """
    Create optimized text for LLM embedding using Approach 1: Single Enhanced Embedding.
    This combines all chunk information into one comprehensive, embedding-ready text.

    Args:
        chunk: Complete chunk with content, text, and metadata

    Returns:
        Enhanced text optimized for embedding generation
    """
    content = chunk.get("content", {})

    # Start with core product information
    embedding_text_parts = []

    # Product name and description (high importance)
    if content.get("name"):
        embedding_text_parts.append(f"Product: {content['name']}")

    if content.get("description_short"):
        embedding_text_parts.append(f"Description: {content['description_short']}")

    if content.get("description_long"):
        # Clean and truncate long description for embedding efficiency
        long_desc = content["description_long"][:500] + "..." if len(content["description_long"]) > 500 else content["description_long"]
        embedding_text_parts.append(f"Details: {long_desc}")

    # Experience highlights (high importance for search)
    if content.get("highlights"):
        embedding_text_parts.append(f"Experience Highlights: {content['highlights']}")

    # Unique value proposition
    if content.get("priceless_value"):
        embedding_text_parts.append(f"What Makes It Special: {content['priceless_value']}")

    # Location information (critical for search)
    location_parts = []
    if content.get("event_venue"):
        location_parts.append(content["event_venue"])
    if content.get("event_city"):
        location_parts.append(content["event_city"])
    if content.get("event_state"):
        location_parts.append(content["event_state"])

    if location_parts:
        embedding_text_parts.append(f"Location: {', '.join(location_parts)}")

    # Provider information
    if content.get("provider_name"):
        embedding_text_parts.append(f"Provider: {content['provider_name']}")

    if content.get("provider_description"):
        # Truncate provider description
        provider_desc = content["provider_description"][:200] + "..." if len(content["provider_description"]) > 200 else content["provider_description"]
        embedding_text_parts.append(f"About Provider: {provider_desc}")

    # Categories and keywords (important for discovery)
    if content.get("primary_category"):
        embedding_text_parts.append(f"Category: {content['primary_category']}")

    if content.get("categories"):
        embedding_text_parts.append(f"All Categories: {content['categories']}")

    if content.get("keywords"):
        embedding_text_parts.append(f"Keywords: {content['keywords']}")

    # Pricing information (often searched)
    if content.get("price") and content.get("currency_code"):
        embedding_text_parts.append(f"Price: {content['price']} {content['currency_code']}")

    # Experience type and features
    if content.get("type"):
        embedding_text_parts.append(f"Experience Type: {content['type']}")

    if content.get("is_experience"):
        embedding_text_parts.append("Format: Experience")

    if content.get("is_event"):
        embedding_text_parts.append("Format: Event")

    # Important terms and conditions (for booking-related queries)
    if content.get("terms_and_conditions"):
        # Extract key terms, limit length
        terms = content["terms_and_conditions"][:300] + "..." if len(content["terms_and_conditions"]) > 300 else content["terms_and_conditions"]
        embedding_text_parts.append(f"Key Terms: {terms}")

    # Validity and timing
    if content.get("end_date"):
        embedding_text_parts.append(f"Valid Until: {content['end_date']}")

    # Capacity information
    if content.get("shared_people_per_item"):
        embedding_text_parts.append(f"For {content['shared_people_per_item']} person(s)")

    # Join all parts with clear separators for embedding
    return "\n".join(embedding_text_parts)

def prepare_embedding_ready_chunks(input_file: str, output_file: str):
    """
    Process chunks and prepare them with embedding-ready text using Approach 1.
    Creates single enhanced embedding text per chunk.

    Args:
        input_file: Path to the input JSON file with chunks
        output_file: Path to save the embedding-ready output
    """
    print(f"Preparing embedding-ready chunks from {input_file}...")

    # Load the input JSON data
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    chunks = data.get("chunks", [])
    embedding_ready_chunks = []

    print(f"Processing {len(chunks)} chunks for embedding preparation...")

    for i, chunk in enumerate(chunks):
        print(f"Processing chunk {i+1}/{len(chunks)}: {chunk.get('product_id', 'Unknown')}")

        # Create embedding-ready text
        embedding_text = create_embedding_ready_text(chunk)

        # Create enhanced chunk structure
        embedding_ready_chunk = {
            "chunk_id": chunk["chunk_id"],
            "product_id": chunk["product_id"],
            "embedding_text": embedding_text,  # Ready for LLM embedding
            "metadata": chunk["metadata"],      # For filtering
            "original_text": chunk["text"],     # For LLM generation
            "content": chunk["content"]         # Full structured data
        }

        embedding_ready_chunks.append(embedding_ready_chunk)

    # Create output structure
    output_data = {
        "chunks": embedding_ready_chunks,
        "embedding_info": {
            "approach": "single_enhanced_embedding",
            "description": "Each chunk has optimized embedding_text ready for LLM embedding",
            "total_chunks": len(embedding_ready_chunks),
            "fields": {
                "embedding_text": "Optimized text for embedding generation",
                "metadata": "Structured data for filtering",
                "original_text": "Original text for LLM response generation",
                "content": "Complete structured product data"
            }
        }
    }

    # Save the embedding-ready data
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Successfully prepared {len(embedding_ready_chunks)} embedding-ready chunks")
    print(f"📁 Output saved to {output_file}")

    # Show sample embedding text
    if embedding_ready_chunks:
        sample_chunk = embedding_ready_chunks[0]
        print(f"\n📝 Sample embedding text for product {sample_chunk['product_id']}:")
        print("-" * 80)
        print(sample_chunk['embedding_text'][:500] + "..." if len(sample_chunk['embedding_text']) > 500 else sample_chunk['embedding_text'])
        print("-" * 80)

    return output_data

def main():
    """Main function to run the script."""
    import argparse

    parser = argparse.ArgumentParser(description="Process product data with NLP cleaning and chunking for RAG")
    parser.add_argument("--input", type=str, default="product-product_id.json", help="Input file path")
    parser.add_argument("--output", type=str, default="rag_cleaned_chunked_products.json", help="Output file path")
    parser.add_argument("--prepare-embeddings", action="store_true", help="Prepare embedding-ready chunks from existing cleaned file")
    parser.add_argument("--embedding-input", type=str, default="rag_cleaned_chunked_products_simplified.json", help="Input file for embedding preparation")
    parser.add_argument("--embedding-output", type=str, default="rag_embedding_ready_chunks.json", help="Output file for embedding-ready chunks")

    args = parser.parse_args()

    try:
        if args.prepare_embeddings:
            # Prepare embedding-ready chunks from existing cleaned file
            prepare_embedding_ready_chunks(args.embedding_input, args.embedding_output)
        else:
            # Process raw product data
            process_product_file(args.input, args.output)
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
