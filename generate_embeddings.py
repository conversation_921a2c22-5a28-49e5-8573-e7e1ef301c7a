import json
import os
import time
from typing import List, Dict, Any, Optional
import openai
from openai import OpenAI
import numpy as np

# Set your OpenAI API key
# OPENAI_API_KEY = "your-api-key-here"  # Replace with your actual API key
# os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY

# Initialize the OpenAI client
client = OpenAI()  # This will use the API key from environment variable

def generate_embedding(text: str, model: str = "text-embedding-ada-002") -> List[float]:
    """
    Generate embeddings for the given text using OpenAI's embedding model.
    
    Args:
        text (str): The text to generate embeddings for
        model (str): The embedding model to use
        
    Returns:
        List[float]: The embedding vector
    """
    if not text.strip():
        # Return a zero vector if text is empty
        return [0.0] * 1536  # ada-002 has 1536 dimensions
    
    try:
        # Call the OpenAI API to generate embeddings
        response = client.embeddings.create(
            input=text,
            model=model
        )
        
        # Extract the embedding from the response
        embedding = response.data[0].embedding
        return embedding
    
    except Exception as e:
        print(f"Error generating embedding: {str(e)}")
        # Return a zero vector in case of error
        return [0.0] * 1536

def prepare_text_for_embedding(product: Dict[str, Any], field: str) -> str:
    """
    Prepare text for embedding based on the field type.
    
    Args:
        product (Dict[str, Any]): The product data
        field (str): The field to prepare text for ('description', 'categories', or 'location')
        
    Returns:
        str: The prepared text
    """
    if field == "description":
        # Combine title, short description, and highlights
        text_parts = [product.get("title", "")]
        
        if "description" in product:
            if "short" in product["description"]:
                text_parts.append(product["description"]["short"])
            
            if "highlights" in product["description"]:
                text_parts.append(" ".join(product["description"]["highlights"]))
                
            if "priceless_value" in product["description"]:
                text_parts.append(product["description"]["priceless_value"])
        
        return " ".join(text_parts)
    
    elif field == "categories":
        # Combine categories and keywords
        text_parts = []
        
        if "categories" in product:
            text_parts.extend(product["categories"])
        
        if "primary_category" in product:
            text_parts.append(product["primary_category"])
            
        if "keywords" in product:
            text_parts.extend(product["keywords"])
            
        return " ".join(text_parts)
    
    elif field == "location":
        # Combine location information
        text_parts = []
        
        if "location" in product:
            location = product["location"]
            
            if "city" in location and location["city"]:
                text_parts.append(location["city"])
                
            if "region" in location and location["region"]:
                text_parts.append(location["region"])
                
            if "country" in location and location["country"]:
                text_parts.append(location["country"])
                
            if "venue" in location and location["venue"]:
                text_parts.append(location["venue"])
        
        return " ".join(text_parts)
    
    return ""

def add_embeddings_to_products(input_file: str, output_file: str, batch_size: int = 10):
    """
    Add vector embeddings to products in the RAG format.
    
    Args:
        input_file (str): Path to the input JSON file
        output_file (str): Path to the output JSON file
        batch_size (int): Number of products to process in each batch
    """
    try:
        # Load the input JSON data
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        products = data.get("products", [])
        total_products = len(products)
        print(f"Processing {total_products} products...")
        
        # Process products in batches to avoid rate limits
        for i in range(0, total_products, batch_size):
            batch = products[i:i+batch_size]
            print(f"Processing batch {i//batch_size + 1}/{(total_products + batch_size - 1)//batch_size}...")
            
            for product in batch:
                # Generate embeddings for each field
                for field in ["description", "categories", "location"]:
                    # Prepare text for embedding
                    text = prepare_text_for_embedding(product, field)
                    
                    # Generate embedding
                    embedding = generate_embedding(text)
                    
                    # Add embedding to product
                    if "vector_embeddings" not in product:
                        product["vector_embeddings"] = {}
                    
                    product["vector_embeddings"][field] = embedding
                    
                    # Print progress
                    print(f"Generated embedding for {field} of product {product.get('id', 'unknown')}")
            
            # Sleep to avoid rate limits
            if i + batch_size < total_products:
                print("Sleeping to avoid rate limits...")
                time.sleep(1)
        
        # Update metadata
        if "metadata" in data:
            data["metadata"]["embedding_model"] = "text-embedding-ada-002"
            data["metadata"]["embeddings_generated_at"] = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        
        # Save the output JSON data
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully added embeddings to {total_products} products in {output_file}")
        
    except Exception as e:
        print(f"Error adding embeddings: {str(e)}")

def add_sample_embeddings(input_file: str, output_file: str, sample_size: int = 3):
    """
    Add sample vector embeddings to a subset of products for demonstration purposes.
    This function doesn't call the OpenAI API but generates random vectors instead.
    
    Args:
        input_file (str): Path to the input JSON file
        output_file (str): Path to the output JSON file
        sample_size (int): Number of products to add sample embeddings to
    """
    try:
        # Load the input JSON data
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        products = data.get("products", [])
        total_products = len(products)
        
        # Process a sample of products
        sample_size = min(sample_size, total_products)
        print(f"Adding sample embeddings to {sample_size} products...")
        
        for i in range(sample_size):
            product = products[i]
            
            # Generate sample embeddings for each field
            for field in ["description", "categories", "location"]:
                # Generate a random embedding (for demonstration)
                # In a real scenario, you would call the OpenAI API
                np.random.seed(int(product.get("id", i)) + hash(field))
                sample_embedding = np.random.normal(0, 0.1, 5).tolist()  # Using only 5 dimensions for brevity
                
                # Add embedding to product
                if "vector_embeddings" not in product:
                    product["vector_embeddings"] = {}
                
                product["vector_embeddings"][field] = sample_embedding
                
                # Print progress
                print(f"Added sample embedding for {field} of product {product.get('id', 'unknown')}")
        
        # Update metadata
        if "metadata" in data:
            data["metadata"]["embedding_model"] = "text-embedding-ada-002 (sample)"
            data["metadata"]["embeddings_generated_at"] = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        
        # Save the output JSON data
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"Successfully added sample embeddings to {sample_size} products in {output_file}")
        
    except Exception as e:
        print(f"Error adding sample embeddings: {str(e)}")

def main():
    """Main function to run the script."""
    input_file = "rag_optimized_product.json"
    output_file = "rag_optimized_product_with_embeddings.json"
    sample_output_file = "rag_optimized_product_with_sample_embeddings.json"
    
    # Check if OpenAI API key is set
    api_key = os.environ.get("OPENAI_API_KEY")
    
    if api_key:
        print("OpenAI API key found. Adding real embeddings...")
        add_embeddings_to_products(input_file, output_file)
    else:
        print("OpenAI API key not found. Adding sample embeddings...")
        add_sample_embeddings(input_file, sample_output_file)
        print("\nTo generate real embeddings, set your OpenAI API key with:")
        print("export OPENAI_API_KEY='your-api-key'  # For Linux/macOS")
        print("set OPENAI_API_KEY=your-api-key  # For Windows")

if __name__ == "__main__":
    main()
