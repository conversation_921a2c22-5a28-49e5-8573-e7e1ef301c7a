import json

def check_missing_products():
    # Load the original product data
    with open('product-product_id.json', 'r', encoding='utf-8') as f:
        original_data = json.load(f)
    
    # Load the optimized product data
    with open('rag_optimized_product.json', 'r', encoding='utf-8') as f:
        optimized_data = json.load(f)
    
    # Extract product IDs from original data
    original_ids = set()
    for product_item in original_data:
        if product_item.get("msg") == "OK" and "data" in product_item:
            product = product_item["data"]
            if "id" in product:
                original_ids.add(str(product["id"]))
    
    # Extract product IDs from optimized data
    optimized_ids = set()
    for product in optimized_data.get("products", []):
        if "id" in product:
            optimized_ids.add(product["id"])
    
    # Find missing products
    missing_ids = original_ids - optimized_ids
    
    # Print results
    print(f"Original products: {len(original_ids)}")
    print(f"Optimized products: {len(optimized_ids)}")
    
    if missing_ids:
        print(f"Missing products: {len(missing_ids)}")
        print("Missing IDs:", sorted(list(missing_ids)))
    else:
        print("No products are missing.")
    
    # Check for extra products
    extra_ids = optimized_ids - original_ids
    if extra_ids:
        print(f"Extra products in optimized file: {len(extra_ids)}")
        print("Extra IDs:", sorted(list(extra_ids)))

if __name__ == "__main__":
    check_missing_products()
