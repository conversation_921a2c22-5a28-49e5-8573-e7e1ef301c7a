# Vector Embeddings for RAG Product Data

This README explains how to use the provided scripts to generate vector embeddings for your product data and perform semantic search.

## Overview

The repository contains three main files:

1. `generate_embeddings.py` - Generates vector embeddings for product data
2. `semantic_search.py` - Performs semantic search using the embeddings
3. `rag_optimized_product.json` - The optimized product data (without embeddings)

## Prerequisites

- Python 3.7+
- OpenAI API key (for generating real embeddings)
- Required Python packages:
  - openai
  - numpy

Install the required packages:

```bash
pip install openai numpy
```

## Generating Embeddings

The `generate_embeddings.py` script replaces the placeholder vector embeddings in your RAG-optimized product data with actual embeddings.

### Using Sample Embeddings (No API Key Required)

If you don't have an OpenAI API key or want to test the workflow without making API calls, you can generate sample embeddings:

```bash
python generate_embeddings.py
```

This will create a file called `rag_optimized_product_with_sample_embeddings.json` with random vector embeddings (for demonstration purposes only).

### Using Real Embeddings (OpenAI API Key Required)

To generate real embeddings using OpenAI's API:

1. Set your OpenAI API key:

   ```bash
   # For Linux/macOS
   export OPENAI_API_KEY='your-api-key'
   
   # For Windows
   set OPENAI_API_KEY=your-api-key
   ```

2. Run the script:

   ```bash
   python generate_embeddings.py
   ```

This will create a file called `rag_optimized_product_with_embeddings.json` with real vector embeddings.

## Performing Semantic Search

The `semantic_search.py` script demonstrates how to use the embeddings for semantic search.

### Basic Usage

```bash
python semantic_search.py --query "underwater dining experience"
```

This will search for products related to "underwater dining experience" using the description embeddings.

### Advanced Options

```bash
python semantic_search.py --query "culinary experiences in Thailand" --embedding-type categories --top-k 3
```

This will search for products related to "culinary experiences in Thailand" using the category embeddings and return the top 3 results.

### Using Real Embeddings for Search

To use real embeddings for the search query (requires OpenAI API key):

```bash
python semantic_search.py --query "luxury travel experience" --use-real-embeddings
```

### All Options

```
--query TEXT               Search query
--embedding-type TYPE      Type of embedding to use (description, categories, location)
--top-k NUMBER             Number of top results to return
--use-real-embeddings      Use real embeddings via OpenAI API
--input-file FILE          Input file with embeddings
```

## Understanding the Embedding Types

The scripts generate three types of embeddings for each product:

1. **Description Embeddings**: Based on the product title, short description, and highlights. Best for finding products that match specific experience descriptions.

2. **Category Embeddings**: Based on the product categories, primary category, and keywords. Best for finding products in specific categories or with specific attributes.

3. **Location Embeddings**: Based on the product location information (city, region, country, venue). Best for finding products in specific locations.

## Example Workflow

1. Convert your product data to the RAG-optimized format using `convert_product_to_rag.py`
2. Generate embeddings using `generate_embeddings.py`
3. Perform semantic search using `semantic_search.py`

## Integration with RAG Systems

To integrate these embeddings with a RAG system:

1. Store the embeddings in a vector database (e.g., Pinecone, Weaviate, Milvus, FAISS)
2. When a user query comes in, convert it to an embedding using the same model
3. Retrieve the most similar products from the vector database
4. Use the retrieved product information to augment the LLM's response

## Notes on Production Use

- The sample embeddings are for demonstration only and should not be used in production
- For production use, consider:
  - Storing embeddings in a dedicated vector database
  - Implementing caching to reduce API calls
  - Adding error handling and retries for API calls
  - Setting up a batch processing pipeline for large datasets
  - Implementing regular updates to keep embeddings in sync with product data
