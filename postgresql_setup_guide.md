# PostgreSQL Setup Guide for Mastercard RAG

## Database Schema and Storage Format

This guide explains how to set up PostgreSQL with pgvector for storing embeddings and conversation history with the 10-conversation window limit.

## 🗄️ Database Schema

### 1. Conversation History Table

```sql
CREATE TABLE conversation_history (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    user_query TEXT NOT NULL,
    assistant_response TEXT NOT NULL,
    response_context JSONB,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    turn_number INTEGER NOT NULL
);

CREATE INDEX idx_session_id ON conversation_history(session_id);
CREATE INDEX idx_session_turn ON conversation_history(session_id, turn_number);
```

**Storage Format:**
- `session_id`: Unique identifier for each conversation session
- `user_query`: User's input text
- `assistant_response`: AI's response text (for display)
- `response_context`: Full JSON context for follow-up questions
- `turn_number`: Sequential number within session (auto-managed)
- `timestamp`: When the conversation turn occurred

### 2. Experience Embeddings Table

```sql
-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

CREATE TABLE experience_embeddings (
    id SERIAL PRIMARY KEY,
    experience_id VARCHAR(255) UNIQUE NOT NULL,
    content_text TEXT NOT NULL,
    embedding VECTOR(384),  -- 384 dimensions for sentence-transformers/all-MiniLM-L6-v2
    metadata JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_experience_id ON experience_embeddings(experience_id);
CREATE INDEX idx_embedding_cosine ON experience_embeddings USING ivfflat (embedding vector_cosine_ops);
```

**Storage Format:**
- `experience_id`: Product ID from Mastercard data
- `content_text`: Full text content for search
- `embedding`: Vector representation (384-dimensional)
- `metadata`: Complete experience data as JSON

### 3. Session Contexts Table

```sql
CREATE TABLE session_contexts (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL,
    experience_id VARCHAR(255),
    context_data JSONB NOT NULL,
    turn_number INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_session_contexts ON session_contexts(session_id, turn_number);
```

**Storage Format:**
- `session_id`: Links to conversation session
- `experience_id`: Reference to specific experience
- `context_data`: Complete context JSON for follow-ups
- `turn_number`: Links to specific conversation turn

## 🔧 PostgreSQL Installation & Setup

### 1. Install PostgreSQL with pgvector

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo apt install postgresql-14-pgvector

# macOS with Homebrew
brew install postgresql
brew install pgvector

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 2. Create Database and User

```bash
# Connect as postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE mastercard_rag;
CREATE USER rag_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE mastercard_rag TO rag_user;

# Connect to the new database
\c mastercard_rag

# Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

# Grant permissions
GRANT ALL ON SCHEMA public TO rag_user;
```

### 3. Environment Configuration

Create `.env` file:

```env
# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=mastercard_rag
POSTGRES_USER=rag_user
POSTGRES_PASSWORD=your_secure_password

# Connection String
DATABASE_URL=postgresql://rag_user:your_secure_password@localhost:5432/mastercard_rag

# RAG Configuration
CONVERSATION_WINDOW_SIZE=10
EMBEDDING_DIMENSION=384
SIMILARITY_THRESHOLD=0.7
```

## 📊 Data Storage Format Examples

### 1. Conversation History Entry

```json
{
  "session_id": "user_123_session",
  "user_query": "Tell me about underwater restaurants in Thailand",
  "assistant_response": "Su Va Na offers an extraordinary underwater dining experience...",
  "response_context": {
    "experience_id": "209677",
    "title": "Soak in privileged treatment at Asia's largest underwater restaurant Vegan Menu",
    "pricing": {"amount": 5900, "currency": "THB", "for_people": 1},
    "location": {"city": "Ko Kaeo", "country": "Thailand", "venue": "Su Va Na Restaurant"},
    "terms_and_conditions": "Priceless experiences are reserved for Mastercard cardholders...",
    "booking_requirements": "Must be booked at least 7 days in advance",
    "valid_until": "2026-04-30"
  },
  "turn_number": 1,
  "timestamp": "2024-01-01T10:00:00Z"
}
```

### 2. Experience Embedding Entry

```json
{
  "experience_id": "209677",
  "content_text": "Id: 209677\nType: experience\nTitle: Soak in privileged treatment...",
  "embedding": [0.1234, -0.5678, 0.9012, ...], // 384-dimensional vector
  "metadata": {
    "chunk_id": "c1cfe6e2-5c58-41be-8270-5c6e3f813500",
    "product_id": "209677",
    "content": {
      "id": "209677",
      "type": "experience",
      "title": "Soak in privileged treatment at Asia's largest underwater restaurant Vegan Menu",
      "pricing_amount": 5900,
      "pricing_currency": "THB",
      "location_city": "Ko Kaeo",
      "location_country": "Thailand"
    }
  }
}
```

## 🔄 10-Conversation Window Management

### Automatic Cleanup Query

```sql
-- This query runs automatically after each new conversation turn
DELETE FROM conversation_history 
WHERE session_id = :session_id 
AND turn_number <= (
    SELECT MAX(turn_number) - 10 
    FROM conversation_history 
    WHERE session_id = :session_id
);

DELETE FROM session_contexts 
WHERE session_id = :session_id 
AND turn_number <= (
    SELECT MAX(turn_number) - 10 
    FROM session_contexts 
    WHERE session_id = :session_id
);
```

### Retrieve Recent Conversations

```sql
-- Get last 10 conversation turns for follow-up context
SELECT user_query, assistant_response, response_context, timestamp, turn_number
FROM conversation_history 
WHERE session_id = :session_id 
ORDER BY turn_number DESC 
LIMIT 10;
```

## 🚀 Performance Optimization

### 1. Vector Search Optimization

```sql
-- Create IVFFlat index for faster similarity search
CREATE INDEX idx_embedding_ivfflat ON experience_embeddings 
USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Analyze table for better query planning
ANALYZE experience_embeddings;
```

### 2. Connection Pooling

```python
# Use connection pooling for better performance
from sqlalchemy.pool import QueuePool

engine = create_engine(
    connection_string,
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True
)
```

### 3. Monitoring Queries

```sql
-- Monitor conversation volume by session
SELECT session_id, COUNT(*) as conversation_count, 
       MAX(timestamp) as last_activity
FROM conversation_history 
GROUP BY session_id 
ORDER BY last_activity DESC;

-- Check embedding storage
SELECT COUNT(*) as total_experiences,
       AVG(array_length(embedding, 1)) as avg_embedding_dim
FROM experience_embeddings;
```

## 🔍 Usage Examples

### 1. Initialize and Store Embeddings

```python
# Initialize with PostgreSQL
rag_chain = MastercardRAGChain(
    experiences_data_path="rag_chunked_product_no_vectors.json",
    connection_string="postgresql://rag_user:password@localhost:5432/mastercard_rag"
)
```

### 2. Query with Context Preservation

```python
# First query - stores context
response1 = rag_chain.process_query(
    "underwater restaurant Thailand", 
    session_id="user_123"
)

# Follow-up query - uses stored context
response2 = rag_chain.process_query(
    "How can I book this?", 
    session_id="user_123"
)
```

### 3. Session Management

```python
# Get conversation history (last 10 turns)
history = rag_chain.get_session_history("user_123")

# Clear session data
rag_chain.clear_session("user_123")
```

This PostgreSQL setup provides robust, scalable storage for both embeddings and conversation history while maintaining the 10-conversation window limit and enabling accurate follow-up responses.
