"""
Mastercard Priceless Experiences RAG Workflow with LangChain
Enhanced JSON Response Format with Session-based Conversation History
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import psycopg2
from psycopg2.extras import RealDictCursor
import numpy as np
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import pandas as pd

from langchain.schema.runnable import RunnablePassthrough
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema.output_parser import StrOutputParser
from langchain_community.chat_models import ChatOllama
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter


@dataclass
class ExperienceContext:
    """Structure for experience context data"""
    experience_id: Optional[str] = None
    title: Optional[str] = None
    pricing: Optional[Dict] = None
    location: Optional[Dict] = None
    terms_and_conditions: Optional[str] = None
    provider: Optional[Dict] = None
    booking_requirements: Optional[str] = None
    valid_until: Optional[str] = None
    media_url: Optional[str] = None
    categories: Optional[List[str]] = None
    keywords: Optional[str] = None


@dataclass
class EnhancedResponse:
    """Enhanced response format with context preservation"""
    response: str
    image_url: Optional[str] = None
    context: Optional[ExperienceContext] = None
    
    def to_dict(self) -> Dict:
        result = {
            "response": self.response,
            "image_url": self.image_url,
            "context": asdict(self.context) if self.context else None
        }
        return result


class PostgreSQLMemoryManager:
    """Manages conversation history and embeddings using PostgreSQL"""

    def __init__(self, connection_string: str, window_size: int = 10):
        self.connection_string = connection_string
        self.window_size = window_size
        self.engine = create_engine(connection_string)
        self.Session = sessionmaker(bind=self.engine)
        self._initialize_tables()

    def _initialize_tables(self):
        """Create necessary tables if they don't exist"""
        with self.engine.connect() as conn:
            # Conversation history table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS conversation_history (
                    id SERIAL PRIMARY KEY,
                    session_id VARCHAR(255) NOT NULL,
                    user_query TEXT NOT NULL,
                    assistant_response TEXT NOT NULL,
                    response_context JSONB,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    turn_number INTEGER NOT NULL
                );
                CREATE INDEX IF NOT EXISTS idx_session_id ON conversation_history(session_id);
                CREATE INDEX IF NOT EXISTS idx_session_turn ON conversation_history(session_id, turn_number);
            """))

            # Experience embeddings table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS experience_embeddings (
                    id SERIAL PRIMARY KEY,
                    experience_id VARCHAR(255) UNIQUE NOT NULL,
                    content_text TEXT NOT NULL,
                    embedding VECTOR(384),
                    metadata JSONB NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                CREATE INDEX IF NOT EXISTS idx_experience_id ON experience_embeddings(experience_id);
            """))

            # Session contexts table for quick access
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS session_contexts (
                    id SERIAL PRIMARY KEY,
                    session_id VARCHAR(255) NOT NULL,
                    experience_id VARCHAR(255),
                    context_data JSONB NOT NULL,
                    turn_number INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                CREATE INDEX IF NOT EXISTS idx_session_contexts ON session_contexts(session_id, turn_number);
            """))

            conn.commit()

    def add_conversation_turn(self, session_id: str, user_query: str,
                           assistant_response: str, response_context: Optional[Dict] = None):
        """Add a conversation turn to the database"""
        with self.engine.connect() as conn:
            # Get current turn number
            result = conn.execute(text("""
                SELECT COALESCE(MAX(turn_number), 0) + 1 as next_turn
                FROM conversation_history
                WHERE session_id = :session_id
            """), {"session_id": session_id})

            turn_number = result.fetchone()[0]

            # Insert new conversation turn
            conn.execute(text("""
                INSERT INTO conversation_history
                (session_id, user_query, assistant_response, response_context, turn_number)
                VALUES (:session_id, :user_query, :assistant_response, :response_context, :turn_number)
            """), {
                "session_id": session_id,
                "user_query": user_query,
                "assistant_response": assistant_response,
                "response_context": json.dumps(response_context) if response_context else None,
                "turn_number": turn_number
            })

            # Store context if available
            if response_context:
                conn.execute(text("""
                    INSERT INTO session_contexts
                    (session_id, experience_id, context_data, turn_number)
                    VALUES (:session_id, :experience_id, :context_data, :turn_number)
                """), {
                    "session_id": session_id,
                    "experience_id": response_context.get("experience_id"),
                    "context_data": json.dumps(response_context),
                    "turn_number": turn_number
                })

            # Clean up old conversations (keep only last 10)
            conn.execute(text("""
                DELETE FROM conversation_history
                WHERE session_id = :session_id
                AND turn_number <= (
                    SELECT MAX(turn_number) - :window_size
                    FROM conversation_history
                    WHERE session_id = :session_id
                )
            """), {"session_id": session_id, "window_size": self.window_size})

            conn.execute(text("""
                DELETE FROM session_contexts
                WHERE session_id = :session_id
                AND turn_number <= (
                    SELECT MAX(turn_number) - :window_size
                    FROM session_contexts
                    WHERE session_id = :session_id
                )
            """), {"session_id": session_id, "window_size": self.window_size})

            conn.commit()

    def get_conversation_history(self, session_id: str) -> List[Dict]:
        """Get last 10 conversation turns for a session"""
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT user_query, assistant_response, response_context, timestamp, turn_number
                FROM conversation_history
                WHERE session_id = :session_id
                ORDER BY turn_number DESC
                LIMIT :limit
            """), {"session_id": session_id, "limit": self.window_size})

            return [dict(row._mapping) for row in result.fetchall()]

    def get_session_contexts(self, session_id: str) -> List[Dict]:
        """Get all contexts for recent conversations in a session"""
        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT DISTINCT context_data
                FROM session_contexts
                WHERE session_id = :session_id
                ORDER BY turn_number DESC
                LIMIT :limit
            """), {"session_id": session_id, "limit": self.window_size})

            contexts = []
            for row in result.fetchall():
                context_data = row[0]
                if context_data:
                    contexts.append(json.loads(context_data) if isinstance(context_data, str) else context_data)
            return contexts

    def clear_session(self, session_id: str):
        """Clear all data for a session"""
        with self.engine.connect() as conn:
            conn.execute(text("DELETE FROM conversation_history WHERE session_id = :session_id"),
                        {"session_id": session_id})
            conn.execute(text("DELETE FROM session_contexts WHERE session_id = :session_id"),
                        {"session_id": session_id})
            conn.commit()


class PostgreSQLVectorStore:
    """PostgreSQL-based vector store for embeddings"""

    def __init__(self, connection_string: str, embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"):
        self.connection_string = connection_string
        self.engine = create_engine(connection_string)
        self.embeddings = HuggingFaceEmbeddings(model_name=embedding_model)

    def store_embeddings(self, experiences_data: Dict):
        """Store experience embeddings in PostgreSQL"""
        with self.engine.connect() as conn:
            for chunk in experiences_data.get("chunks", []):
                text_content = chunk.get("text", "")
                if not text_content:
                    continue

                # Generate embedding
                embedding = self.embeddings.embed_query(text_content)

                # Store in database
                conn.execute(text("""
                    INSERT INTO experience_embeddings
                    (experience_id, content_text, embedding, metadata)
                    VALUES (:experience_id, :content_text, :embedding, :metadata)
                    ON CONFLICT (experience_id) DO UPDATE SET
                    content_text = EXCLUDED.content_text,
                    embedding = EXCLUDED.embedding,
                    metadata = EXCLUDED.metadata
                """), {
                    "experience_id": chunk.get("product_id"),
                    "content_text": text_content,
                    "embedding": embedding,
                    "metadata": json.dumps({
                        "chunk_id": chunk.get("chunk_id"),
                        "product_id": chunk.get("product_id"),
                        "content": chunk.get("content", {})
                    })
                })
            conn.commit()

    def similarity_search(self, query: str, k: int = 5) -> List[Dict]:
        """Perform similarity search using PostgreSQL vector operations"""
        query_embedding = self.embeddings.embed_query(query)

        with self.engine.connect() as conn:
            result = conn.execute(text("""
                SELECT experience_id, content_text, metadata,
                       embedding <-> :query_embedding as distance
                FROM experience_embeddings
                ORDER BY embedding <-> :query_embedding
                LIMIT :k
            """), {
                "query_embedding": query_embedding,
                "k": k
            })

            documents = []
            for row in result.fetchall():
                metadata = json.loads(row[2]) if isinstance(row[2], str) else row[2]
                documents.append({
                    "page_content": row[1],
                    "metadata": metadata,
                    "distance": row[3]
                })

            return documents


class MastercardRAGChain:
    """Main RAG chain for Mastercard Priceless Experiences with PostgreSQL backend"""

    def __init__(self, experiences_data_path: str, connection_string: str, model_name: str = "llama-maverick"):
        self.memory_manager = PostgreSQLMemoryManager(connection_string)
        self.experiences_data = self._load_experiences_data(experiences_data_path)
        self.vectorstore = PostgreSQLVectorStore(connection_string)
        self.llm = ChatOllama(model=model_name, temperature=0.1)

        # Initialize embeddings if not already done
        self._initialize_embeddings()
        self.chain = self._setup_chain()

    def _load_experiences_data(self, data_path: str) -> Dict:
        """Load Mastercard experiences data"""
        with open(data_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _initialize_embeddings(self):
        """Initialize embeddings in PostgreSQL if not already done"""
        try:
            # Check if embeddings exist
            with self.vectorstore.engine.connect() as conn:
                result = conn.execute(text("SELECT COUNT(*) FROM experience_embeddings"))
                count = result.fetchone()[0]

                if count == 0:
                    print("Initializing embeddings in PostgreSQL...")
                    self.vectorstore.store_embeddings(self.experiences_data)
                    print(f"✅ Stored embeddings for {len(self.experiences_data.get('chunks', []))} experiences")
                else:
                    print(f"✅ Found {count} existing embeddings in PostgreSQL")
        except Exception as e:
            print(f"❌ Error initializing embeddings: {e}")
            raise
    
    def _setup_chain(self):
        """Setup the RAG chain with enhanced prompt"""

        # Load system prompt
        with open("mastercard_rag_system_prompt.md", "r", encoding="utf-8") as f:
            system_prompt = f.read()

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            ("human", """
            Previous Experience Contexts: {previous_contexts}

            Retrieved Documents: {context}

            Conversation History: {chat_history}

            User Query: {question}

            Provide response in the exact JSON format specified in the system prompt.
            """)
        ])

        def format_docs(docs):
            if not docs:
                return "No relevant documents found."
            return "\n\n".join([doc.get("page_content", "") for doc in docs])

        def get_previous_contexts(session_id: str) -> str:
            contexts = self.memory_manager.get_session_contexts(session_id)
            if not contexts:
                return "No previous experience contexts."
            return json.dumps(contexts, indent=2)

        def get_chat_history(session_id: str) -> str:
            history = self.memory_manager.get_conversation_history(session_id)
            if not history:
                return "No previous conversation."

            formatted_history = []
            for turn in reversed(history):  # Reverse to get chronological order
                formatted_history.append(f"User: {turn['user_query']}")
                formatted_history.append(f"Assistant: {turn['assistant_response']}")

            return "\n".join(formatted_history[-20:])  # Last 10 turns (20 messages)

        def retrieve_documents(query: str) -> List[Dict]:
            return self.vectorstore.similarity_search(query, k=5)

        chain = (
            {
                "context": lambda x: format_docs(retrieve_documents(x["question"])),
                "question": RunnablePassthrough(),
                "chat_history": lambda x: get_chat_history(x["session_id"]),
                "previous_contexts": lambda x: get_previous_contexts(x["session_id"])
            }
            | prompt
            | self.llm
            | StrOutputParser()
        )

        return chain
    
    def process_query(self, query: str, session_id: str = None) -> EnhancedResponse:
        """Process user query with PostgreSQL session management"""

        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())

        try:
            # Run the chain
            response = self.chain.invoke({
                "question": query,
                "session_id": session_id
            })

            # Parse JSON response
            parsed_response = json.loads(response)

            # Create enhanced response object
            enhanced_response = EnhancedResponse(
                response=parsed_response.get("response", ""),
                image_url=parsed_response.get("image_url"),
                context=ExperienceContext(**parsed_response["context"]) if parsed_response.get("context") else None
            )

            # Store conversation turn in PostgreSQL
            context_dict = asdict(enhanced_response.context) if enhanced_response.context else None
            self.memory_manager.add_conversation_turn(
                session_id=session_id,
                user_query=query,
                assistant_response=enhanced_response.response,
                response_context=context_dict
            )

            return enhanced_response

        except json.JSONDecodeError:
            # Fallback for non-JSON responses
            fallback_response = EnhancedResponse(
                response="I apologize, but I encountered an error processing your request. Please try again.",
                image_url=None,
                context=None
            )

            # Still store the conversation turn
            self.memory_manager.add_conversation_turn(
                session_id=session_id,
                user_query=query,
                assistant_response=fallback_response.response,
                response_context=None
            )

            return fallback_response

        except Exception as e:
            error_response = EnhancedResponse(
                response=f"An error occurred: {str(e)}",
                image_url=None,
                context=None
            )

            # Store error in conversation history
            self.memory_manager.add_conversation_turn(
                session_id=session_id,
                user_query=query,
                assistant_response=error_response.response,
                response_context=None
            )

            return error_response
    
    def get_session_history(self, session_id: str) -> List[Dict]:
        """Get conversation history for a session from PostgreSQL"""
        history = self.memory_manager.get_conversation_history(session_id)

        formatted_history = []
        for turn in reversed(history):  # Reverse to get chronological order
            formatted_history.append({
                "user": turn["user_query"],
                "assistant": turn["assistant_response"],
                "timestamp": turn["timestamp"].isoformat() if hasattr(turn["timestamp"], 'isoformat') else str(turn["timestamp"]),
                "turn_number": turn["turn_number"],
                "context": json.loads(turn["response_context"]) if turn["response_context"] else None
            })

        return formatted_history

    def clear_session(self, session_id: str):
        """Clear session history from PostgreSQL"""
        self.memory_manager.clear_session(session_id)


# Usage Example
def main():
    """Example usage of the PostgreSQL RAG workflow"""

    # PostgreSQL connection string
    connection_string = "postgresql://username:password@localhost:5432/mastercard_rag"

    # Initialize the RAG chain with PostgreSQL backend
    rag_chain = MastercardRAGChain(
        experiences_data_path="rag_chunked_product_no_vectors.json",
        connection_string=connection_string
    )

    # Example conversation with session management
    session_id = "user_123_session"

    # Query 1: Initial query
    response1 = rag_chain.process_query(
        "Tell me about underwater restaurants in Thailand",
        session_id=session_id
    )
    print("Response 1:", json.dumps(response1.to_dict(), indent=2))

    # Query 2: Follow-up query (uses context from previous response)
    response2 = rag_chain.process_query(
        "How can I book this experience?",
        session_id=session_id
    )
    print("Response 2:", json.dumps(response2.to_dict(), indent=2))

    # Query 3: Another follow-up
    response3 = rag_chain.process_query(
        "What's the exact address?",
        session_id=session_id
    )
    print("Response 3:", json.dumps(response3.to_dict(), indent=2))

    # Get session history
    history = rag_chain.get_session_history(session_id)
    print("Session History:", json.dumps(history, indent=2))


if __name__ == "__main__":
    main()
