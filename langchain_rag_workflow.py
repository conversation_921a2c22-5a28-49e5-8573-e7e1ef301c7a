"""
Mastercard Priceless Experiences RAG Workflow with LangChain
Enhanced JSON Response Format with Session-based Conversation History
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from langchain.memory import ConversationBufferWindowMemory
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from langchain.callbacks.base import BaseCallbackHandler
from langchain.schema.runnable import RunnablePassthrough
from langchain.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain.schema.output_parser import StrOutputParser
from langchain_community.chat_models import ChatOllama
from langchain_community.vectorstores import FAISS
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain.text_splitter import RecursiveCharacterTextSplitter


@dataclass
class ExperienceContext:
    """Structure for experience context data"""
    experience_id: Optional[str] = None
    title: Optional[str] = None
    pricing: Optional[Dict] = None
    location: Optional[Dict] = None
    terms_and_conditions: Optional[str] = None
    provider: Optional[Dict] = None
    booking_requirements: Optional[str] = None
    valid_until: Optional[str] = None
    media_url: Optional[str] = None
    categories: Optional[List[str]] = None
    keywords: Optional[str] = None


@dataclass
class EnhancedResponse:
    """Enhanced response format with context preservation"""
    response: str
    image_url: Optional[str] = None
    context: Optional[ExperienceContext] = None
    
    def to_dict(self) -> Dict:
        result = {
            "response": self.response,
            "image_url": self.image_url,
            "context": asdict(self.context) if self.context else None
        }
        return result


class SessionMemoryManager:
    """Manages conversation history with session IDs"""
    
    def __init__(self, window_size: int = 10):
        self.sessions: Dict[str, ConversationBufferWindowMemory] = {}
        self.session_contexts: Dict[str, List[ExperienceContext]] = {}
        self.window_size = window_size
    
    def get_or_create_session(self, session_id: str) -> ConversationBufferWindowMemory:
        """Get existing session or create new one"""
        if session_id not in self.sessions:
            self.sessions[session_id] = ConversationBufferWindowMemory(
                k=self.window_size,
                return_messages=True,
                memory_key="chat_history"
            )
            self.session_contexts[session_id] = []
        return self.sessions[session_id]
    
    def add_context(self, session_id: str, context: ExperienceContext):
        """Add experience context to session"""
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = []
        self.session_contexts[session_id].append(context)
    
    def get_session_contexts(self, session_id: str) -> List[ExperienceContext]:
        """Get all contexts for a session"""
        return self.session_contexts.get(session_id, [])
    
    def clear_session(self, session_id: str):
        """Clear session history and contexts"""
        if session_id in self.sessions:
            del self.sessions[session_id]
        if session_id in self.session_contexts:
            del self.session_contexts[session_id]


class MastercardRAGChain:
    """Main RAG chain for Mastercard Priceless Experiences"""
    
    def __init__(self, experiences_data_path: str, model_name: str = "llama-maverick"):
        self.memory_manager = SessionMemoryManager()
        self.experiences_data = self._load_experiences_data(experiences_data_path)
        self.vectorstore = self._setup_vectorstore()
        self.llm = ChatOllama(model=model_name, temperature=0.1)
        self.chain = self._setup_chain()
    
    def _load_experiences_data(self, data_path: str) -> Dict:
        """Load Mastercard experiences data"""
        with open(data_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _setup_vectorstore(self) -> FAISS:
        """Setup FAISS vectorstore with experiences data"""
        # Extract text content from experiences
        texts = []
        metadatas = []
        
        for chunk in self.experiences_data.get("chunks", []):
            texts.append(chunk.get("text", ""))
            metadatas.append({
                "chunk_id": chunk.get("chunk_id"),
                "product_id": chunk.get("product_id"),
                "content": chunk.get("content", {})
            })
        
        # Create embeddings and vectorstore
        embeddings = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L6-v2")
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        split_texts = text_splitter.split_texts(texts)
        
        vectorstore = FAISS.from_texts(split_texts, embeddings, metadatas=metadatas)
        return vectorstore
    
    def _setup_chain(self):
        """Setup the RAG chain with enhanced prompt"""
        
        # Load system prompt
        with open("mastercard_rag_system_prompt.md", "r", encoding="utf-8") as f:
            system_prompt = f.read()
        
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", """
            Previous Experience Contexts: {previous_contexts}
            
            Retrieved Documents: {context}
            
            User Query: {question}
            
            Provide response in the exact JSON format specified in the system prompt.
            """)
        ])
        
        def format_docs(docs):
            return "\n\n".join(doc.page_content for doc in docs)
        
        def get_previous_contexts(session_id: str) -> str:
            contexts = self.memory_manager.get_session_contexts(session_id)
            if not contexts:
                return "No previous experience contexts."
            return json.dumps([asdict(ctx) for ctx in contexts], indent=2)
        
        chain = (
            {
                "context": self.vectorstore.as_retriever() | format_docs,
                "question": RunnablePassthrough(),
                "chat_history": lambda x: self.memory_manager.get_or_create_session(x["session_id"]).chat_memory.messages,
                "previous_contexts": lambda x: get_previous_contexts(x["session_id"])
            }
            | prompt
            | self.llm
            | StrOutputParser()
        )
        
        return chain
    
    def process_query(self, query: str, session_id: str = None) -> EnhancedResponse:
        """Process user query with session management"""
        
        # Generate session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
        
        # Get session memory
        memory = self.memory_manager.get_or_create_session(session_id)
        
        try:
            # Run the chain
            response = self.chain.invoke({
                "question": query,
                "session_id": session_id
            })
            
            # Parse JSON response
            parsed_response = json.loads(response)
            
            # Create enhanced response object
            enhanced_response = EnhancedResponse(
                response=parsed_response.get("response", ""),
                image_url=parsed_response.get("image_url"),
                context=ExperienceContext(**parsed_response["context"]) if parsed_response.get("context") else None
            )
            
            # Add to memory
            memory.chat_memory.add_user_message(query)
            memory.chat_memory.add_ai_message(enhanced_response.response)
            
            # Store context if available
            if enhanced_response.context:
                self.memory_manager.add_context(session_id, enhanced_response.context)
            
            return enhanced_response
            
        except json.JSONDecodeError:
            # Fallback for non-JSON responses
            return EnhancedResponse(
                response="I apologize, but I encountered an error processing your request. Please try again.",
                image_url=None,
                context=None
            )
        except Exception as e:
            return EnhancedResponse(
                response=f"An error occurred: {str(e)}",
                image_url=None,
                context=None
            )
    
    def get_session_history(self, session_id: str) -> List[Dict]:
        """Get conversation history for a session"""
        if session_id not in self.memory_manager.sessions:
            return []
        
        messages = self.memory_manager.sessions[session_id].chat_memory.messages
        history = []
        
        for i in range(0, len(messages), 2):
            if i + 1 < len(messages):
                history.append({
                    "user": messages[i].content,
                    "assistant": messages[i + 1].content,
                    "timestamp": datetime.now().isoformat()
                })
        
        return history
    
    def clear_session(self, session_id: str):
        """Clear session history"""
        self.memory_manager.clear_session(session_id)


# Usage Example
def main():
    """Example usage of the RAG workflow"""
    
    # Initialize the RAG chain
    rag_chain = MastercardRAGChain("rag_chunked_product_no_vectors.json")
    
    # Example conversation with session management
    session_id = "user_123_session"
    
    # Query 1: Initial query
    response1 = rag_chain.process_query(
        "Tell me about underwater restaurants in Thailand",
        session_id=session_id
    )
    print("Response 1:", json.dumps(response1.to_dict(), indent=2))
    
    # Query 2: Follow-up query (uses context from previous response)
    response2 = rag_chain.process_query(
        "How can I book this experience?",
        session_id=session_id
    )
    print("Response 2:", json.dumps(response2.to_dict(), indent=2))
    
    # Query 3: Another follow-up
    response3 = rag_chain.process_query(
        "What's the exact address?",
        session_id=session_id
    )
    print("Response 3:", json.dumps(response3.to_dict(), indent=2))
    
    # Get session history
    history = rag_chain.get_session_history(session_id)
    print("Session History:", json.dumps(history, indent=2))


if __name__ == "__main__":
    main()
