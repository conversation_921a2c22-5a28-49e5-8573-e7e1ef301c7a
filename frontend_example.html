<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mastercard Priceless Experiences Assistant</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .chat-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #ff5f00, #eb001b);
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            text-align: center;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .user-message {
            background: #007bff;
            color: white;
            align-self: flex-end;
            margin-left: auto;
        }
        
        .assistant-message {
            background: #f1f3f4;
            color: #333;
            align-self: flex-start;
        }
        
        .message img {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .chat-input {
            display: flex;
            padding: 20px;
            border-top: 1px solid #eee;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        
        .chat-input button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .chat-input button:hover {
            background: #0056b3;
        }
        
        .chat-input button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }
        
        .session-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 12px;
            color: #1976d2;
        }
        
        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h2>🏆 Mastercard Priceless Experiences</h2>
            <p>Your AI Assistant for Exclusive Experiences</p>
        </div>
        
        <div class="session-info">
            Session ID: <span id="sessionId">Generating...</span>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant-message">
                Hello! I'm your Mastercard Priceless Experiences assistant. I can help you discover exclusive dining, travel, entertainment, and cultural experiences available to cardholders. What type of experience are you looking for today?
            </div>
        </div>
        
        <div class="loading" id="loading">
            Assistant is typing...
        </div>
        
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="Ask about experiences, locations, or booking details..." />
            <button onclick="sendMessage()" id="sendButton">Send</button>
        </div>
    </div>

    <script>
        // Global variables
        let sessionId = null;
        const API_BASE_URL = 'http://localhost:8000';
        
        // Initialize session
        function initializeSession() {
            sessionId = 'session_' + Math.random().toString(36).substr(2, 9);
            document.getElementById('sessionId').textContent = sessionId;
        }
        
        // Send message function
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';
            
            // Show loading
            showLoading(true);
            
            try {
                const response = await fetch(`${API_BASE_URL}/query`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: message,
                        session_id: sessionId
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Add assistant response to chat
                addMessage(data.response, 'assistant', data.image_url);
                
                // Store context in session storage for debugging
                if (data.context) {
                    sessionStorage.setItem(`context_${Date.now()}`, JSON.stringify(data.context));
                }
                
            } catch (error) {
                console.error('Error:', error);
                addErrorMessage('Sorry, I encountered an error. Please try again.');
            } finally {
                showLoading(false);
            }
        }
        
        // Add message to chat
        function addMessage(text, sender, imageUrl = null) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            let content = text;
            if (imageUrl) {
                content += `<br><img src="${imageUrl}" alt="Experience Image" />`;
            }
            
            messageDiv.innerHTML = content;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Add error message
        function addErrorMessage(text) {
            const messagesContainer = document.getElementById('chatMessages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = text;
            messagesContainer.appendChild(errorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
        
        // Show/hide loading indicator
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const sendButton = document.getElementById('sendButton');
            
            loading.style.display = show ? 'block' : 'none';
            sendButton.disabled = show;
        }
        
        // Handle Enter key press
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initialize on page load
        window.onload = function() {
            initializeSession();
        };
        
        // Example queries for testing
        function sendExampleQuery(query) {
            document.getElementById('messageInput').value = query;
            sendMessage();
        }
        
        // Add example buttons (optional)
        document.addEventListener('DOMContentLoaded', function() {
            const examples = [
                "Hello",
                "Tell me about underwater restaurants in Thailand",
                "Show me culinary experiences in Paris",
                "What travel experiences are available in Netherlands?"
            ];
            
            // You can add these as quick action buttons if desired
        });
    </script>
</body>
</html>
