"""
Database initialization script for Mastercard RAG with PostgreSQL
Creates tables, indexes, and initial setup
"""

import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine, text
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_database_if_not_exists():
    """Create database if it doesn't exist"""
    
    # Connection parameters
    host = os.getenv('POSTGRES_HOST', 'localhost')
    port = os.getenv('POSTGRES_PORT', '5432')
    user = os.getenv('POSTGRES_USER', 'postgres')
    password = os.getenv('POSTGRES_PASSWORD', 'password')
    database = os.getenv('POSTGRES_DB', 'mastercard_rag')
    
    # Connect to PostgreSQL server (not specific database)
    conn = psycopg2.connect(
        host=host,
        port=port,
        user=user,
        password=password,
        database='postgres'  # Connect to default database first
    )
    conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
    
    cursor = conn.cursor()
    
    # Check if database exists
    cursor.execute("SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s", (database,))
    exists = cursor.fetchone()
    
    if not exists:
        print(f"Creating database: {database}")
        cursor.execute(f'CREATE DATABASE "{database}"')
        print(f"✅ Database {database} created successfully")
    else:
        print(f"✅ Database {database} already exists")
    
    cursor.close()
    conn.close()


def initialize_tables():
    """Initialize all required tables and extensions"""
    
    connection_string = os.getenv('DATABASE_URL')
    if not connection_string:
        host = os.getenv('POSTGRES_HOST', 'localhost')
        port = os.getenv('POSTGRES_PORT', '5432')
        user = os.getenv('POSTGRES_USER', 'postgres')
        password = os.getenv('POSTGRES_PASSWORD', 'password')
        database = os.getenv('POSTGRES_DB', 'mastercard_rag')
        connection_string = f"postgresql://{user}:{password}@{host}:{port}/{database}"
    
    engine = create_engine(connection_string)
    
    with engine.connect() as conn:
        print("Initializing database tables and extensions...")
        
        # Enable pgvector extension
        try:
            conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector;"))
            print("✅ pgvector extension enabled")
        except Exception as e:
            print(f"⚠️  Warning: Could not enable pgvector extension: {e}")
            print("   Make sure pgvector is installed on your PostgreSQL server")
        
        # Create conversation_history table
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS conversation_history (
                id SERIAL PRIMARY KEY,
                session_id VARCHAR(255) NOT NULL,
                user_query TEXT NOT NULL,
                assistant_response TEXT NOT NULL,
                response_context JSONB,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                turn_number INTEGER NOT NULL
            );
        """))
        
        # Create indexes for conversation_history
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_session_id ON conversation_history(session_id);
        """))
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_session_turn ON conversation_history(session_id, turn_number);
        """))
        
        print("✅ conversation_history table created")
        
        # Create experience_embeddings table
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS experience_embeddings (
                id SERIAL PRIMARY KEY,
                experience_id VARCHAR(255) UNIQUE NOT NULL,
                content_text TEXT NOT NULL,
                embedding VECTOR(384),
                metadata JSONB NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """))
        
        # Create indexes for experience_embeddings
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_experience_id ON experience_embeddings(experience_id);
        """))
        
        try:
            # Create vector index (requires pgvector)
            conn.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_embedding_cosine 
                ON experience_embeddings USING ivfflat (embedding vector_cosine_ops)
                WITH (lists = 100);
            """))
            print("✅ experience_embeddings table with vector index created")
        except Exception as e:
            print(f"✅ experience_embeddings table created (without vector index: {e})")
        
        # Create session_contexts table
        conn.execute(text("""
            CREATE TABLE IF NOT EXISTS session_contexts (
                id SERIAL PRIMARY KEY,
                session_id VARCHAR(255) NOT NULL,
                experience_id VARCHAR(255),
                context_data JSONB NOT NULL,
                turn_number INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """))
        
        # Create indexes for session_contexts
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_session_contexts ON session_contexts(session_id, turn_number);
        """))
        
        print("✅ session_contexts table created")
        
        conn.commit()
        print("✅ All tables and indexes created successfully")


def verify_setup():
    """Verify database setup"""
    
    connection_string = os.getenv('DATABASE_URL')
    if not connection_string:
        host = os.getenv('POSTGRES_HOST', 'localhost')
        port = os.getenv('POSTGRES_PORT', '5432')
        user = os.getenv('POSTGRES_USER', 'postgres')
        password = os.getenv('POSTGRES_PASSWORD', 'password')
        database = os.getenv('POSTGRES_DB', 'mastercard_rag')
        connection_string = f"postgresql://{user}:{password}@{host}:{port}/{database}"
    
    engine = create_engine(connection_string)
    
    with engine.connect() as conn:
        print("\nVerifying database setup...")
        
        # Check tables
        tables = ['conversation_history', 'experience_embeddings', 'session_contexts']
        for table in tables:
            result = conn.execute(text(f"""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name = '{table}'
            """))
            count = result.fetchone()[0]
            if count > 0:
                print(f"✅ Table {table} exists")
            else:
                print(f"❌ Table {table} missing")
        
        # Check pgvector extension
        try:
            result = conn.execute(text("SELECT extname FROM pg_extension WHERE extname = 'vector'"))
            if result.fetchone():
                print("✅ pgvector extension is installed")
            else:
                print("⚠️  pgvector extension not found")
        except Exception as e:
            print(f"⚠️  Could not check pgvector extension: {e}")
        
        # Check indexes
        result = conn.execute(text("""
            SELECT indexname FROM pg_indexes 
            WHERE tablename IN ('conversation_history', 'experience_embeddings', 'session_contexts')
        """))
        indexes = [row[0] for row in result.fetchall()]
        print(f"✅ Found {len(indexes)} indexes")
        
        print("\n🎉 Database setup verification complete!")


def main():
    """Main initialization function"""
    print("🚀 Starting Mastercard RAG Database Initialization")
    print("=" * 50)
    
    try:
        # Step 1: Create database if needed
        create_database_if_not_exists()
        
        # Step 2: Initialize tables
        initialize_tables()
        
        # Step 3: Verify setup
        verify_setup()
        
        print("\n" + "=" * 50)
        print("✅ Database initialization completed successfully!")
        print("\nNext steps:")
        print("1. Update your .env file with the correct database credentials")
        print("2. Run the RAG application to populate embeddings")
        print("3. Test the conversation flow")
        
    except Exception as e:
        print(f"\n❌ Database initialization failed: {e}")
        print("\nTroubleshooting:")
        print("1. Check your PostgreSQL server is running")
        print("2. Verify database credentials in .env file")
        print("3. Ensure pgvector extension is installed")
        raise


if __name__ == "__main__":
    main()
