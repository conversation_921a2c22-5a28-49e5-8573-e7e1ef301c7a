# Mastercard Priceless Experiences RAG Setup Guide

## Complete LangChain Workflow with Session Management

This guide provides a complete implementation of the enhanced JSON response format with session-based conversation history using LangChain libraries.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   FastAPI        │    │   <PERSON><PERSON><PERSON><PERSON>     │
│   (HTML/JS)     │◄──►│   Service        │◄──►│   RAG Chain     │
│                 │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │   Session        │    │   Vector Store  │
                       │   Memory         │    │   (FAISS)       │
                       │   Manager        │    │                 │
                       └──────────────────┘    └─────────────────┘
```

## 📋 Prerequisites

1. **Python 3.8+**
2. **Llama Maverick model** (or compatible Ollama model)
3. **Mastercard experiences JSON data**

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Setup Ollama with Llama Maverick

```bash
# Install Ollama (if not already installed)
curl -fsSL https://ollama.ai/install.sh | sh

# Pull Llama Maverick model
ollama pull llama-maverick
```

### 3. Prepare Data

Ensure your `rag_chunked_product_no_vectors.json` file is in the project directory.

### 4. Start the Service

```bash
python fastapi_rag_service.py
```

The service will start on `http://localhost:8000`

### 5. Open Frontend

Open `frontend_example.html` in your browser to test the chat interface.

## 🔧 Configuration

### Environment Variables

Create a `.env` file:

```env
# Model Configuration
OLLAMA_MODEL=llama-maverick
OLLAMA_BASE_URL=http://localhost:11434

# Service Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true

# Memory Configuration
CONVERSATION_WINDOW_SIZE=10
MAX_SESSIONS=1000

# Vector Store Configuration
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

## 📊 Enhanced JSON Response Format

### Response Structure

```json
{
  "response": "User-facing display text",
  "image_url": "URL for display image or null",
  "context": {
    "experience_id": "209677",
    "title": "Experience title",
    "pricing": {
      "amount": 5900,
      "currency": "THB", 
      "for_people": 1
    },
    "location": {
      "city": "Ko Kaeo",
      "country": "Thailand",
      "venue": "Su Va Na Restaurant",
      "address": "Full address"
    },
    "terms_and_conditions": "Complete terms text",
    "provider": {
      "name": "Provider name",
      "description": "Provider description"
    },
    "booking_requirements": "Booking details",
    "valid_until": "2026-04-30",
    "media_url": "https://demo.priceless.com/p/209677",
    "categories": ["Culinary", "Global"],
    "keywords": "relevant keywords"
  }
}
```

### Context Preservation Rules

1. **Experience-Specific Queries**: Include full context for follow-up questions
2. **General Conversations**: Use `"context": null`
3. **Multi-Experience Responses**: Include array of contexts

## 🔄 Session Management Workflow

### 1. Session Creation

```python
# Automatic session creation
session_id = str(uuid.uuid4())
memory = SessionMemoryManager().get_or_create_session(session_id)
```

### 2. Context Storage

```python
# Store experience context for follow-ups
if enhanced_response.context:
    memory_manager.add_context(session_id, enhanced_response.context)
```

### 3. Follow-up Processing

```python
# Retrieve previous contexts for follow-up questions
previous_contexts = memory_manager.get_session_contexts(session_id)
```

## 🧪 Testing the Implementation

### 1. Basic Conversation Flow

```bash
# Test greeting
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Hello", "session_id": "test_session"}'

# Test specific query
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "underwater restaurant Thailand", "session_id": "test_session"}'

# Test follow-up
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "How can I book this?", "session_id": "test_session"}'
```

### 2. Session Management

```bash
# Get session history
curl "http://localhost:8000/session/test_session/history"

# Clear session
curl -X DELETE "http://localhost:8000/session/test_session"

# List active sessions
curl "http://localhost:8000/sessions"
```

## 🎯 Key Features

### ✅ Enhanced Response Format
- Clean user interface with `response` and `image_url`
- Complete context preservation for follow-ups
- Structured data for accurate conversation continuity

### ✅ Session Management
- Unique session IDs for each conversation
- Conversation window management (configurable size)
- Context accumulation across conversation turns

### ✅ Follow-up Intelligence
- "How can I book this?" uses stored terms and conditions
- "What's the address?" uses stored location data
- "Tell me more" uses stored provider descriptions

### ✅ Memory Optimization
- Sliding window memory (default: 10 messages)
- Context pruning for performance
- Session cleanup capabilities

## 🔍 Conversation Examples

### Example 1: Complete Flow

**Turn 1:**
```
User: "Hello"
Response: {
  "response": "Hello! I'm your Mastercard Priceless Experiences assistant...",
  "image_url": null,
  "context": null
}
```

**Turn 2:**
```
User: "underwater restaurant Thailand"
Response: {
  "response": "Su Va Na offers extraordinary underwater dining in Phuket...",
  "image_url": "https://...",
  "context": { /* full experience data */ }
}
```

**Turn 3:**
```
User: "How can I book this?"
Response: {
  "response": "To book Su Va Na, make a reservation at least 7 days in advance...",
  "image_url": null,
  "context": { /* booking-specific context */ }
}
```

## 🚀 Production Deployment

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000
CMD ["gunicorn", "fastapi_rag_service:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "-b", "0.0.0.0:8000"]
```

### Environment Configuration

```yaml
# docker-compose.yml
version: '3.8'
services:
  rag-service:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
    depends_on:
      - ollama
  
  ollama:
    image: ollama/ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama

volumes:
  ollama_data:
```

## 🔧 Troubleshooting

### Common Issues

1. **Model Not Found**: Ensure Llama Maverick is pulled in Ollama
2. **JSON Parse Error**: Check system prompt formatting
3. **Memory Issues**: Reduce conversation window size
4. **Session Conflicts**: Implement session cleanup

### Performance Optimization

1. **Vector Store**: Use GPU-accelerated FAISS for large datasets
2. **Caching**: Implement Redis for session storage
3. **Load Balancing**: Use multiple worker processes
4. **Monitoring**: Add logging and metrics

This implementation provides a complete, production-ready RAG system with enhanced JSON responses and robust session management for accurate follow-up conversations.
