import json
import statistics
from typing import Dict, List, Any, Tuple
import re

def analyze_product_structure(file_path: str):
    """Analyze the structure of products in the RAG-optimized JSON file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    products = data.get("products", [])
    total_products = len(products)
    
    # Analyze nesting depth
    max_depth = 0
    depths = []
    
    # Analyze text length
    text_lengths = []
    
    # Analyze field distribution
    field_counts = {}
    
    for product in products:
        depth = get_max_nesting_depth(product)
        depths.append(depth)
        max_depth = max(max_depth, depth)
        
        text_length = get_total_text_length(product)
        text_lengths.append(text_length)
        
        count_fields(product, field_counts)
    
    # Print results
    print(f"Total products: {total_products}")
    print(f"Maximum nesting depth: {max_depth}")
    print(f"Average nesting depth: {sum(depths) / len(depths):.2f}")
    print(f"Median nesting depth: {statistics.median(depths)}")
    print()
    
    print(f"Text length statistics:")
    print(f"  Minimum: {min(text_lengths)} characters")
    print(f"  Maximum: {max(text_lengths)} characters")
    print(f"  Average: {sum(text_lengths) / len(text_lengths):.2f} characters")
    print(f"  Median: {statistics.median(text_lengths)} characters")
    print()
    
    print("Top fields by frequency:")
    sorted_fields = sorted(field_counts.items(), key=lambda x: x[1], reverse=True)
    for field, count in sorted_fields[:10]:
        print(f"  {field}: {count}")
    
    # Analyze if flattening is needed
    analyze_flattening_need(products)
    
    # Recommend chunking strategy
    recommend_chunking_strategy(products, text_lengths, max_depth)

def get_max_nesting_depth(obj, current_depth=1):
    """Get the maximum nesting depth of a JSON object."""
    if not isinstance(obj, dict) and not isinstance(obj, list):
        return current_depth
    
    max_depth = current_depth
    
    if isinstance(obj, dict):
        for key, value in obj.items():
            if isinstance(value, (dict, list)):
                depth = get_max_nesting_depth(value, current_depth + 1)
                max_depth = max(max_depth, depth)
    
    elif isinstance(obj, list):
        for item in obj:
            if isinstance(item, (dict, list)):
                depth = get_max_nesting_depth(item, current_depth + 1)
                max_depth = max(max_depth, depth)
    
    return max_depth

def get_total_text_length(obj):
    """Get the total length of all text fields in a JSON object."""
    if isinstance(obj, str):
        return len(obj)
    
    if isinstance(obj, dict):
        return sum(get_total_text_length(value) for value in obj.values())
    
    if isinstance(obj, list):
        return sum(get_total_text_length(item) for item in obj)
    
    return 0

def count_fields(obj, counts, prefix=""):
    """Count the frequency of fields in a JSON object."""
    if isinstance(obj, dict):
        for key, value in obj.items():
            field_name = f"{prefix}.{key}" if prefix else key
            counts[field_name] = counts.get(field_name, 0) + 1
            
            if isinstance(value, (dict, list)):
                count_fields(value, counts, field_name)
    
    elif isinstance(obj, list) and len(obj) > 0 and isinstance(obj[0], dict):
        for item in obj:
            count_fields(item, counts, prefix)

def analyze_flattening_need(products):
    """Analyze if the products need to be flattened."""
    # Check for deep nesting
    deep_nesting = False
    for product in products:
        if get_max_nesting_depth(product) > 3:
            deep_nesting = True
            break
    
    # Check for array fields that might need to be flattened
    array_fields = set()
    for product in products:
        for key, value in product.items():
            if isinstance(value, list) and len(value) > 0 and not isinstance(value[0], (dict, list)):
                array_fields.add(key)
    
    # Check for nested objects that might need to be flattened
    nested_objects = set()
    for product in products:
        for key, value in product.items():
            if isinstance(value, dict):
                nested_objects.add(key)
    
    print("\nFlattening Analysis:")
    print(f"  Deep nesting detected: {'Yes' if deep_nesting else 'No'}")
    print(f"  Array fields that could be flattened: {', '.join(array_fields) if array_fields else 'None'}")
    print(f"  Nested objects that could be flattened: {', '.join(nested_objects) if nested_objects else 'None'}")
    
    # Make recommendation
    if deep_nesting or nested_objects:
        print("\nRecommendation: Flattening is recommended before chunking.")
        print("  - Nested objects like 'location', 'description', and 'pricing' should be flattened")
        print("  - Arrays like 'highlights', 'categories', and 'terms_and_conditions' should be joined into strings")
    else:
        print("\nRecommendation: Flattening is not necessary before chunking.")

def recommend_chunking_strategy(products, text_lengths, max_depth):
    """Recommend a chunking strategy based on the analysis."""
    avg_text_length = sum(text_lengths) / len(text_lengths)
    
    print("\nChunking Strategy Recommendation:")
    
    # Determine if product-level chunking is sufficient
    if avg_text_length < 2000 and max_depth <= 3:
        print("1. Product-Level Chunking (Recommended)")
        print("   - Each product should be treated as a single chunk")
        print("   - Advantages: Preserves all relationships within a product")
        print("   - Disadvantages: May not be optimal for very large products")
        
        print("\n2. Field-Based Chunking (Alternative)")
        print("   - Group related fields into semantic chunks")
        print("   - Example chunks: Basic Info, Description, Location, Pricing, Provider")
        print("   - Advantages: More granular retrieval, smaller chunks")
        print("   - Disadvantages: May lose some context between fields")
    else:
        print("1. Field-Based Chunking (Recommended)")
        print("   - Group related fields into semantic chunks")
        print("   - Example chunks: Basic Info, Description, Location, Pricing, Provider")
        print("   - Advantages: More granular retrieval, smaller chunks")
        print("   - Disadvantages: May lose some context between fields")
        
        print("\n2. Product-Level Chunking (Alternative)")
        print("   - Each product should be treated as a single chunk")
        print("   - Advantages: Preserves all relationships within a product")
        print("   - Disadvantages: May not be optimal for very large products")
    
    print("\n3. Hybrid Chunking (Advanced)")
    print("   - Store both product-level and field-level chunks")
    print("   - Use field-level chunks for initial retrieval")
    print("   - Expand to product-level chunks for context")
    print("   - Advantages: Flexible retrieval, preserves context")
    print("   - Disadvantages: More complex implementation, more storage")
    
    # Provide specific implementation guidance
    print("\nImplementation Guidance:")
    print("1. Flatten the JSON structure first")
    print("2. Create a unique ID for each chunk that references the product ID")
    print("3. Include metadata with each chunk (product ID, chunk type, etc.)")
    print("4. Consider using overlapping chunks for important fields")
    print("5. Maintain vector embeddings at both product and chunk levels")

if __name__ == "__main__":
    analyze_product_structure("rag_optimized_product.json")
