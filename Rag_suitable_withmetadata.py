import json

def extract_relevant_info(json_data, fields):
    """
    Extract relevant information from the JSON data based on the specified fields.

    Args:
        json_data (list): List of JSON objects containing product data.
        fields (list): List of fields to extract from each product.

    Returns:
        list: List of dictionaries containing the extracted information.
    """
    extracted_data = []

    for item in json_data:
        if item.get("msg") == "OK" and "data" in item:
            product_data = item["data"]
            extracted_item = {field: product_data.get(field, None) for field in fields}
            extracted_data.append(extracted_item)

    return extracted_data


def merge_metadata(extracted_data):
    """
    Merge metadata into the extracted data for RAG.

    Args:
        extracted_data (list): List of dictionaries containing extracted information.

    Returns:
        list: List of dictionaries with metadata merged.
    """
    refined_data = []
    for item in extracted_data:
        metadata = {
            "id": item.get("id"),
            "displayName": item.get("displayName"),
            "categories": [cat.get("name") for cat in item.get("categories", []) if cat],
            "location": {
                "city": item.get("eventCity"),
                "state": item.get("eventState"),
                "venue": item.get("eventVenue"),
                "latitude": item.get("latitude"),
                "longitude": item.get("longitude"),
            },
            "pricing": {
                "price": item.get("price"),
                "currency": item.get("currencyCode"),
            },
            "media": {
                "url": item.get("url"),
                "image": item.get("imageURL"),
            },
        }
        # Merge metadata with the original item
        refined_item = {**item, "metadata": metadata}
        refined_data.append(refined_item)

    return refined_data


def save_refined_data(refined_data, output_path):
    """
    Save the refined data to a new JSON file.

    Args:
        refined_data (list): List of dictionaries with refined data.
        output_path (str): Path to save the output JSON file.
    """
    try:
        with open(output_path, "w", encoding="utf-8") as file:
            json.dump(refined_data, file, indent=4, ensure_ascii=False)
        print(f"Refined output saved to {output_path}")
    except Exception as e:
        print(f"Error while saving the file: {e}")


# Main execution
if __name__ == "__main__":
    try:
        # Load the JSON data (replace 'product-product_id.json' with your file path)
        input_file_path = "c:/Users/<USER>/Desktop/mastercard/product-product_id.json"
        with open(input_file_path, "r", encoding="utf-8") as file:
            json_data = json.load(file)

        # Specify the fields you want to extract
        fields_to_extract = [
            "id",
            "displayName",
            "price",
            "currencyCode",
            "longDescription",
            "eventCity",
            "eventState",
            "eventVenue",
            "latitude",
            "longitude",
            "url",
            "imageURL",
            "details",
            "terms",
            "categories",
            "primaryCategory",
            "vendor",  # Added vendor details
            "eventAddress",  # Added event address details
        ]

        # Extract relevant information
        extracted_info = extract_relevant_info(json_data, fields_to_extract)

        # Debugging: Print extracted information
        print("Extracted information:", extracted_info)

        # Merge metadata into the extracted information
        refined_data = merge_metadata(extracted_info)

        # Debugging: Print refined data
        print("Refined data:", refined_data)

        # Save the refined data to a new JSON file
        output_file_path = "c:/Users/<USER>/Desktop/mastercard/refined_output.json"
        save_refined_data(refined_data, output_file_path)

    except FileNotFoundError:
        print(f"Error: The file {input_file_path} was not found. Please check the file path.")
    except json.JSONDecodeError:
        print("Error: Failed to decode JSON. Please ensure the input file contains valid JSON.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")